#include "csvreader.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QDebug>
#include <QVariantMap>
#include <QDateTime>

CsvReader::CsvReader(QObject *parent) : QObject(parent)
{
}

QString CsvReader::buildFilePath(const QDate &date)
{
    QString fileName = QString("boiler_%1.csv").arg(date.toString("yyyyMMdd"));
    return QDir::currentPath() + "/data/" + fileName;
}

QVariantList CsvReader::readDataByDate(const QDate &date)
{
    QVariantList result;
    QString filePath = buildFilePath(date);

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件:" << filePath;
        return result;
    }

    QTextStream in(&file);
    in.setCodec("UTF-8");

    // 读取表头
    QString headerLine = in.readLine();
    if (headerLine.isEmpty()) {
        qDebug() << "文件为空或无法读取表头:" << filePath;
        return result;
    }

    QStringList headers = headerLine.split(',');

    // 读取数据行
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (!line.isEmpty()) {
            QVariantMap dataMap = parseDataLine(line, headers);
            if (!dataMap.isEmpty()) {
                result.append(dataMap);
            }
        }
    }

    file.close();
    qDebug() << "成功读取" << result.size() << "条数据，日期:" << date.toString("yyyy-MM-dd");
    return result;
}

QVariantMap CsvReader::parseDataLine(const QString &line, const QStringList &headers)
{
    QVariantMap dataMap;
    QStringList values = line.split(',');

    if (values.size() != headers.size()) {
        qDebug() << "数据列数与表头不匹配:" << values.size() << "vs" << headers.size();
        return dataMap;
    }

    for (int i = 0; i < headers.size(); ++i) {
        QString header = headers[i].trimmed();
        QString value = values[i].trimmed();

        // 时间戳特殊处理
        if (header == "时间戳") {
            bool ok;
            qint64 timestamp = value.toLongLong(&ok);
            if (ok) {
                QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
                dataMap[header] = dateTime.toString("yyyy-MM-dd hh:mm:ss");
                dataMap["timestamp"] = timestamp;
            } else {
                dataMap[header] = value;
            }
        } else {
            // 尝试转换为数字
            bool ok;
            double numValue = value.toDouble(&ok);
            if (ok) {
                dataMap[header] = numValue;
            } else {
                dataMap[header] = value;
            }
        }
    }

    return dataMap;
}

QVariantList CsvReader::getAvailableDates()
{
    QVariantList dates;
    QDir dataDir(QDir::currentPath() + "/data");

    if (!dataDir.exists()) {
        qDebug() << "数据目录不存在:" << dataDir.path();
        return dates;
    }

    QStringList filters;
    filters << "boiler_*.csv";
    QStringList files = dataDir.entryList(filters, QDir::Files, QDir::Name);

    foreach (const QString &fileName, files) {
        // 从文件名提取日期: boiler_20250801.csv -> 20250801
        QString dateStr = fileName.mid(7, 8); // 跳过"boiler_"，取8位日期
        QDate date = QDate::fromString(dateStr, "yyyyMMdd");
        if (date.isValid()) {
            dates.append(date.toString("yyyy-MM-dd"));
        }
    }

    qDebug() << "找到" << dates.size() << "个历史数据文件";
    return dates;
}

bool CsvReader::hasDataForDate(const QDate &date)
{
    QString filePath = buildFilePath(date);
    QFile file(filePath);
    return file.exists();
}

QVariantList CsvReader::readDataByDateRange(const QDate &startDate, const QDate &endDate)
{
    QVariantList result;

    QDate currentDate = startDate;
    while (currentDate <= endDate) {
        if (hasDataForDate(currentDate)) {
            QVariantList dayData = readDataByDate(currentDate);
            result.append(dayData);
        }
        currentDate = currentDate.addDays(1);
    }

    return result;
}