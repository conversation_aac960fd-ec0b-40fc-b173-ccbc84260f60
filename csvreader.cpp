#include "csvreader.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QDebug>
#include <QVariantMap>
#include <QDateTime>

CsvReader::CsvReader(QObject *parent) : QObject(parent)
{
}

QString CsvReader::buildFilePath(const QDate &date)
{
    QString fileName = QString("boiler_%1.csv").arg(date.toString("yyyyMMdd"));
    return QDir::currentPath() + "/data/" + fileName;
}

QVariantList CsvReader::readDataByDate(const QDate &date)
{
    QVariantList result;
    QString filePath = buildFilePath(date);

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件:" << filePath;
        return result;
    }

    // 预估文件大小，预分配内存以提高性能
    qint64 fileSize = file.size();
    int estimatedRows = qMax(1, (int)(fileSize / 100));  // 估算每行约100字节
    result.reserve(estimatedRows);

    QTextStream in(&file);
    in.setCodec("UTF-8");

    // 读取表头
    QString headerLine = in.readLine();
    if (headerLine.isEmpty()) {
        qDebug() << "文件为空或无法读取表头:" << filePath;
        return result;
    }

    QStringList headers = headerLine.split(',');

    // 批量读取数据，减少频繁的内存分配
    QStringList lines;
    const int batchSize = 1000;  // 每批处理1000行

    while (!in.atEnd()) {
        lines.clear();

        // 读取一批数据
        for (int i = 0; i < batchSize && !in.atEnd(); ++i) {
            QString line = in.readLine();
            if (!line.isEmpty()) {
                lines.append(line);
            }
        }

        // 批量解析这批数据
        for (const QString &line : lines) {
            QVariantMap dataMap = parseDataLine(line, headers);
            if (!dataMap.isEmpty()) {
                result.append(dataMap);
            }
        }
    }

    file.close();
    qDebug() << "成功读取" << result.size() << "条数据，日期:" << date.toString("yyyy-MM-dd");
    return result;
}

QVariantMap CsvReader::parseDataLine(const QString &line, const QStringList &headers)
{
    QVariantMap dataMap;
    QStringList values = line.split(',');

    if (values.size() != headers.size()) {
        qDebug() << "数据列数与表头不匹配:" << values.size() << "vs" << headers.size();
        return dataMap;
    }

    // 预分配map容量以提高性能
    dataMap.reserve(headers.size());

    for (int i = 0; i < headers.size(); ++i) {
        const QString &header = headers[i];  // 避免不必要的拷贝
        const QString &value = values[i];    // 避免不必要的拷贝

        // 减少trimmed()调用，只在必要时使用
        QString trimmedHeader = header.trimmed();
        QString trimmedValue = value.trimmed();

        // 时间戳特殊处理
        if (trimmedHeader == "时间戳") {
            bool ok;
            qint64 timestamp = trimmedValue.toLongLong(&ok);
            if (ok) {
                QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
                dataMap[trimmedHeader] = dateTime.toString("yyyy-MM-dd hh:mm:ss");
                dataMap["timestamp"] = timestamp;
            } else {
                dataMap[trimmedHeader] = trimmedValue;
            }
        } else {
            // 尝试转换为数字，优化数字检测
            if (!trimmedValue.isEmpty() && (trimmedValue[0].isDigit() || trimmedValue[0] == '-' || trimmedValue[0] == '.')) {
                bool ok;
                double numValue = trimmedValue.toDouble(&ok);
                if (ok) {
                    dataMap[trimmedHeader] = numValue;
                } else {
                    dataMap[trimmedHeader] = trimmedValue;
                }
            } else {
                dataMap[trimmedHeader] = trimmedValue;
            }
        }
    }

    return dataMap;
}

QVariantList CsvReader::getAvailableDates()
{
    QVariantList dates;
    QDir dataDir(QDir::currentPath() + "/data");

    if (!dataDir.exists()) {
        qDebug() << "数据目录不存在:" << dataDir.path();
        return dates;
    }

    QStringList filters;
    filters << "boiler_*.csv";
    QStringList files = dataDir.entryList(filters, QDir::Files, QDir::Name);

    foreach (const QString &fileName, files) {
        // 从文件名提取日期: boiler_20250801.csv -> 20250801
        QString dateStr = fileName.mid(7, 8); // 跳过"boiler_"，取8位日期
        QDate date = QDate::fromString(dateStr, "yyyyMMdd");
        if (date.isValid()) {
            dates.append(date.toString("yyyy-MM-dd"));
        }
    }

    qDebug() << "找到" << dates.size() << "个历史数据文件";
    return dates;
}

bool CsvReader::hasDataForDate(const QDate &date)
{
    QString filePath = buildFilePath(date);
    QFile file(filePath);
    return file.exists();
}

QVariantList CsvReader::readDataByDateRange(const QDate &startDate, const QDate &endDate)
{
    QVariantList result;

    QDate currentDate = startDate;
    while (currentDate <= endDate) {
        if (hasDataForDate(currentDate)) {
            QVariantList dayData = readDataByDate(currentDate);
            result.append(dayData);
        }
        currentDate = currentDate.addDays(1);
    }

    return result;
}